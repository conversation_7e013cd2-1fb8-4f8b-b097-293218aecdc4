#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""

from agent_ppo.conf.conf import Config
from kaiwu_agent.utils.common_func import create_cls, attached
import numpy as np
from typing import List

# The create_cls function is used to dynamically create a class.
# The first parameter of the function is the type name, and the remaining parameters are the attributes of the class.
# The default value of the attribute should be set to None.
# create_cls函数用于动态创建一个类，函数第一个参数为类型名称，剩余参数为类的属性，属性默认值应设为None
ObsData = create_cls(
    "ObsData",
    feature=None,
    legal_action=None,
    reward=None,
)


ActData = create_cls(
    "ActData",
    probs=None,
    value=None,
    target=None,
    predict=None,
    action=None,
    prob=None,
)

SampleData = create_cls("SampleData", npdata=None)

RelativeDistance = {
    "RELATIVE_DISTANCE_NONE": 0,
    "VerySmall": 1,
    "Small": 2,
    "Medium": 3,
    "Large": 4,
    "VeryLarge": 5,
}


RelativeDirection = {
    "East": 1,
    "NorthEast": 2,
    "North": 3,
    "NorthWest": 4,
    "West": 5,
    "SouthWest": 6,
    "South": 7,
    "SouthEast": 8,
}

DirectionAngles = {
    1: 0,
    2: 45,
    3: 90,
    4: 135,
    5: 180,
    6: 225,
    7: 270,
    8: 315,
}


def reward_process(cur_obs, 
                    cur_extra_info, 
                    prev_obs, 
                    prev_extra_info, 
                    local_memory_map,
                    last_target,
                    target,
                    ):
    
    # step reward
    # 步数奖励
    step_reward = -0.01
    memory_reward = 0.0
    if local_memory_map[5,5] >= 0.3:
        memory_reward = -local_memory_map[5,5]

    stop_reward = 0.0
    talent_reward = 0.0
    
    buff_reward = 0
    dist_reward = 0
    if target is not None and prev_obs is not None:
        if prev_obs['frame_state']['heroes'][0]['pos'] == cur_obs['frame_state']['heroes'][0]['pos']:
            stop_reward = -0.5

        if cur_obs['score_info']['treasure_collected_count'] > prev_obs['score_info']['treasure_collected_count']:
            return [5.0]
        if cur_obs['score_info']['buff_count'] > prev_obs['score_info']['buff_count']:
            buff_reward = 0.5 ** cur_obs['score_info']['buff_count']
        
        if target['pos'][0] != None:
            cur_pos = cur_obs['frame_state']['heroes'][0]['pos']
            prev_pos = prev_obs['frame_state']['heroes'][0]['pos']
            prev_dist = abs(prev_pos['x'] - target['pos'][0]) + abs(prev_pos['z'] - target['pos'][1])
            cur_dist = abs(cur_pos['x'] - target['pos'][0]) + abs(cur_pos['z'] - target['pos'][1])
            dist_reward = prev_dist - cur_dist
        
        if cur_obs['score_info']['talent_count'] > prev_obs['score_info']['talent_count']:
            talent_reward = -0.5

    
    #print(dist_reward)
    return [step_reward + memory_reward + stop_reward + talent_reward + buff_reward + dist_reward]


class SampleManager:
    def __init__(
        self,
        gamma=0.99,
        tdlambda=0.95,
    ):
        self.gamma = Config.GAMMA
        self.tdlambda = Config.TDLAMBDA

        self.feature = []
        self.probs = []
        self.actions = []
        self.reward = []
        self.value = []
        self.adv = []
        self.tdlamret = []
        self.legal_action = []
        self.count = 0
        self.samples = []

    def add(self, feature, legal_action, prob, action, value, reward):
        self.feature.append(feature)
        self.legal_action.append(legal_action)
        self.probs.append(prob)
        self.actions.append(action)
        self.value.append(value)
        self.reward.append(reward)
        self.adv.append(np.zeros_like(value))
        self.tdlamret.append(np.zeros_like(value))
        self.count += 1

    def add_last_reward(self, reward):
        self.reward.append(reward)
        self.value.append(np.zeros_like(reward))

    def update_sample_info(self):
        last_gae = 0
        for i in range(self.count - 1, -1, -1):
            reward = self.reward[i + 1]
            next_val = self.value[i + 1]
            val = self.value[i]
            delta = reward + next_val * self.gamma - val
            last_gae = delta + self.gamma * self.tdlambda * last_gae
            self.adv[i] = last_gae
            self.tdlamret[i] = last_gae + val
        
        tdlamret_array = np.array(self.tdlamret)
        if len(tdlamret_array) > 0:
            tdlamret_mean = tdlamret_array.mean()
            tdlamret_std = tdlamret_array.std()
            # Standardize returns: (returns - mean) / (std + 1e-8)
            standardized_tdlamret = (tdlamret_array - tdlamret_mean) / (tdlamret_std + 1e-8)
            self.tdlamret = standardized_tdlamret.tolist()
        
        adv_array = np.array(self.adv)
        if len(adv_array) > 0:
            adv_mean = adv_array.mean()
            adv_std = adv_array.std()
            standardized_adv = (adv_array - adv_mean) / (adv_std + 1e-8)
            self.adv = standardized_adv.tolist()

    def sample_process(self, feature, legal_action, prob, action, value, reward):
        self.add(feature, legal_action, prob, action, value, reward)

    def process_last_frame(self, reward):
        self.add_last_reward(reward)
        # 发送前的后向传递更新
        # Backward pass updates before sending
        self.update_sample_info()
        self.samples = self._get_game_data()

    def get_game_data(self):
        ret = self.samples
        self.samples = []
        return ret

    def _get_game_data(self):
        feature = np.array(self.feature).transpose()
        probs = np.array(self.probs).transpose()
        actions = np.array(self.actions).transpose()
        reward = np.array(self.reward[:-1]).transpose()
        value = np.array(self.value[:-1]).transpose()
        legal_action = np.array(self.legal_action).transpose()
        adv = np.array(self.adv).transpose()
        tdlamret = np.array(self.tdlamret).transpose()

        data = np.concatenate([feature, reward, value, tdlamret, adv, actions, probs, legal_action]).transpose()

        samples = []
        for i in range(0, self.count):
            samples.append(SampleData(npdata=data[i].astype(np.float32)))

        return samples


@attached
def SampleData2NumpyData(g_data):
    return g_data.npdata


@attached
def NumpyData2SampleData(s_data):
    return SampleData(npdata=s_data)
